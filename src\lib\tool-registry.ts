
import { AgeCalculatorTool } from '@/components/tools/AgeCalculatorTool';
import { AgeDifferenceCalculatorTool } from '@/components/tools/AgeDifferenceCalculatorTool';
import { AngerTestTool } from '@/components/tools/AngerTestTool';
import { ArabicNameDecoratorTool } from '@/components/tools/ArabicNameDecoratorTool';
import { AramcoStockTool } from '@/components/tools/AramcoStockTool';
import { AverageCalculatorTool } from '@/components/tools/AverageCalculatorTool';
import { BackgroundRemovalTool } from '@/components/tools/BackgroundRemovalTool';
import { BmiCalculatorTool } from '@/components/tools/BmiCalculatorTool';
import { CaloriesCalculatorTool } from '@/components/tools/CaloriesCalculatorTool';
import { CbmCalculatorTool } from '@/components/tools/CbmCalculatorTool';
import { ColorBlindnessTestTool } from '@/components/tools/ColorBlindnessTestTool';
import { CountdownCardTool } from '@/components/tools/CountdownCardTool';
import { CurrencyConverterTool } from '@/components/tools/CurrencyConverterTool';
import { DateConverterTool } from '@/components/tools/DateConverterTool';
import { DateDifferenceTool } from '@/components/tools/DateDifferenceTool';
import { DiscountCalculatorTool } from '@/components/tools/DiscountCalculatorTool';
import { FaddanToMeterConverterTool } from '@/components/tools/FaddanToMeterConverterTool';
import { FeetToMeterConverterTool } from '@/components/tools/FeetToMeterConverterTool';
import { FemininityTestTool } from '@/components/tools/FemininityTestTool';
import { FinancialAidRequestTool } from '@/components/tools/FinancialAidRequestTool';
import { FriendshipTestTool } from '@/components/tools/FriendshipTestTool';
import { GallonToLiterConverterTool } from '@/components/tools/GallonToLiterConverterTool';
import { GoldPriceTool } from '@/components/tools/GoldPriceTool';
import { GpaCalculatorTool } from '@/components/tools/GpaCalculatorTool';
import { HourlyWageCalculatorTool } from '@/components/tools/HourlyWageCalculatorTool';
import { InvisibleCharacterTool } from '@/components/tools/InvisibleCharacterTool';
import { IslamicQuizTool } from '@/components/tools/IslamicQuizTool';
import { IstikharaPrayerTool } from '@/components/tools/IstikharaPrayerTool';
import { JealousyTestTool } from '@/components/tools/JealousyTestTool';
import { JordanianTawjihiCalculatorTool } from '@/components/tools/JordanianTawjihiCalculatorTool';
import { KidsQuizTool } from '@/components/tools/KidsQuizTool';
import { LoveTestTool } from '@/components/tools/LoveTestTool';
import { MasculinityTestTool } from '@/components/tools/MasculinityTestTool';
import { MileKilometerConverterTool } from '@/components/tools/MileKilometerConverterTool';
import { MultiplicationTableTool } from '@/components/tools/MultiplicationTableTool';
import { MyIpTool } from '@/components/tools/MyIpTool';
import { NumberToWordsTool } from '@/components/tools/NumberToWordsTool';
import { OmaniDakhiliyahDialectTestTool } from '@/components/tools/OmaniDakhiliyahDialectTestTool';
import { OunceToGramConverterTool } from '@/components/tools/OunceToGramConverterTool';
import { OvertimeCalculatorTool } from '@/components/tools/OvertimeCalculatorTool';
import { OvulationCalculatorTool } from '@/components/tools/OvulationCalculatorTool';
import { ParaphraseTextTool } from '@/components/tools/ParaphraseTextTool';
import { PercentageCalculatorTool } from '@/components/tools/PercentageCalculatorTool';
import { PersonalityStrengthTestTool } from '@/components/tools/PersonalityStrengthTestTool';
import { PoundToKgConverterTool } from '@/components/tools/PoundToKgConverterTool';
import { PregnancyCalculatorTool } from '@/components/tools/PregnancyCalculatorTool';
import { ProteinCalculatorTool } from '@/components/tools/ProteinCalculatorTool';
import { QrCodeGeneratorTool } from '@/components/tools/QrCodeGeneratorTool';
import { QrCodeReaderTool } from '@/components/tools/QrCodeReaderTool';
import { ResignationLetterGeneratorTool } from '@/components/tools/ResignationLetterGeneratorTool';
import { RetirementCalculatorTool } from '@/components/tools/RetirementCalculatorTool';
import { ReverseTextTool } from '@/components/tools/ReverseTextTool';
import { SampleSizeCalculatorTool } from '@/components/tools/SampleSizeCalculatorTool';
import { SensitivityTestTool } from '@/components/tools/SensitivityTestTool';
import { SimpleCalculatorTool } from '@/components/tools/SimpleCalculatorTool';
import { SpoiledTestTool } from '@/components/tools/SpoiledTestTool';
import { SqrtCalculatorTool } from '@/components/tools/SqrtCalculatorTool';
import { SummarizeArabicTextTool } from '@/components/tools/SummarizeArabicTextTool';
import { TextRepeaterTool } from '@/components/tools/TextRepeaterTool';
import { TodaysDateTool } from '@/components/tools/TodaysDateTool';
import { UnitConverterTool } from '@/components/tools/UnitConverterTool';
import { VatCalculatorTool } from '@/components/tools/VatCalculatorTool';
import { WeightedGradeCalculatorTool } from '@/components/tools/WeightedGradeCalculatorTool';
import { WhatsappToolsTool } from '@/components/tools/WhatsappToolsTool';
import { WordCountTool } from '@/components/tools/WordCountTool';
import { ZakatCalculatorTool } from '@/components/tools/ZakatCalculatorTool';
import { ZodiacSignCalculatorTool } from '@/components/tools/ZodiacSignCalculatorTool';
import { InvestmentCalculatorTool } from '@/components/tools/InvestmentCalculatorTool';
import { MacronutrientCalculatorTool } from '@/components/tools/MacronutrientCalculatorTool';
import { SpiritAnimalTestTool } from '@/components/tools/SpiritAnimalTestTool';
import type { ComponentType } from 'react';

const toolComponentMap: { [key: string]: ComponentType<any> } = {
    'zakat-calculator': ZakatCalculatorTool,
    'vat-calculator': VatCalculatorTool,
    'currency-converter': CurrencyConverterTool,
    'gold-price': GoldPriceTool,
    'aramco-stock': AramcoStockTool,
    'background-removal': BackgroundRemovalTool,
    'discount-calculator': DiscountCalculatorTool,
    'cbm-calculator': CbmCalculatorTool,
    'saudi-salary-countdown': CountdownCardTool,
    'hourly-wage-calculator': HourlyWageCalculatorTool,
    'overtime-calculator': OvertimeCalculatorTool,
    'retirement-calculator': RetirementCalculatorTool,
    'investment-calculator': InvestmentCalculatorTool,
    'citizen-account-countdown': CountdownCardTool,
    'retirement-pension-countdown': CountdownCardTool,
    'housing-support-countdown': CountdownCardTool,
    'unit-converter': UnitConverterTool,
    'mile-kilometer-converter': MileKilometerConverterTool,
    'faddan-to-meter-converter': FaddanToMeterConverterTool,
    'gallon-to-liter-converter': GallonToLiterConverterTool,
    'feet-to-meter-converter': FeetToMeterConverterTool,
    'pound-to-kg-converter': PoundToKgConverterTool,
    'ounce-to-gram-converter': OunceToGramConverterTool,
    'bmi-calculator': BmiCalculatorTool,
    'calories-calculator': CaloriesCalculatorTool,
    'protein-calculator': ProteinCalculatorTool,
    'macronutrient-calculator': MacronutrientCalculatorTool,
    'pregnancy-calculator': PregnancyCalculatorTool,
    'ovulation-calculator': OvulationCalculatorTool,
    'todays-date': TodaysDateTool,
    'ramadan-countdown': CountdownCardTool,
    'eid-alfitr-countdown': CountdownCardTool,
    'eid-aladha-countdown': CountdownCardTool,
    'arafah-day-countdown': CountdownCardTool,
    'founding-day-countdown': CountdownCardTool,
    'saudi-national-day-countdown': CountdownCardTool,
    'next-vacation-countdown': CountdownCardTool,
    'study-calendar-countdown': CountdownCardTool,
    'winter-countdown': CountdownCardTool,
    'summer-end-countdown': CountdownCardTool,
    'age-calculator': AgeCalculatorTool,
    'date-difference': DateDifferenceTool,
    'age-difference-calculator': AgeDifferenceCalculatorTool,
    'date-converter': DateConverterTool,
    'multiplication-table': MultiplicationTableTool,
    'gpa-calculator': GpaCalculatorTool,
    'weighted-grade-calculator': WeightedGradeCalculatorTool,
    'jordanian-tawjihi-calculator': JordanianTawjihiCalculatorTool,
    'sample-size-calculator': SampleSizeCalculatorTool,
    'summarize-arabic-text': SummarizeArabicTextTool,
    'paraphrase-text': ParaphraseTextTool,
    'resignation-letter-generator': ResignationLetterGeneratorTool,
    'financial-aid-request-generator': FinancialAidRequestTool,
    'word-count': WordCountTool,
    'reverse-text': ReverseTextTool,
    'text-repeater': TextRepeaterTool,
    'invisible-character': InvisibleCharacterTool,
    'arabic-name-decorator': ArabicNameDecoratorTool,
    'percentage-calculator': PercentageCalculatorTool,
    'simple-calculator': SimpleCalculatorTool,
    'sqrt-calculator': SqrtCalculatorTool,
    'average-calculator': AverageCalculatorTool,
    'color-blindness-test': ColorBlindnessTestTool,
    'omani-dakhiliyah-dialect-test': OmaniDakhiliyahDialectTestTool,
    'personality-strength-test': PersonalityStrengthTestTool,
    'jealousy-test': JealousyTestTool,
    'anger-test': AngerTestTool,
    'love-test': LoveTestTool,
    'friendship-test': FriendshipTestTool,
    'masculinity-test': MasculinityTestTool,
    'femininity-test': FemininityTestTool,
    'spoiled-test': SpoiledTestTool,
    'sensitivity-test': SensitivityTestTool,
    'spirit-animal-test': SpiritAnimalTestTool,
    'qr-code-generator': QrCodeGeneratorTool,
    'qr-code-reader': QrCodeReaderTool,
    'my-ip': MyIpTool,
    'istikhara-prayer': IstikharaPrayerTool,
    'islamic-quiz': IslamicQuizTool,
    'kids-quiz': KidsQuizTool,
    'zodiac-sign-calculator': ZodiacSignCalculatorTool,
    'whatsapp-tools': WhatsappToolsTool,
};

export function getToolComponent(slug: string): ComponentType<any> | undefined {
    return toolComponentMap[slug];
}
