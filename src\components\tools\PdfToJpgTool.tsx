'use client';

import { useState, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, Download, Trash2, AlertTriangle, FileText, Image as ImageIcon, Loader2, ExternalLink } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface OnlineService {
  name: string;
  url: string;
  description: string;
  features: string[];
}

export function PdfToJpgTool() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showOnlineServices, setShowOnlineServices] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const onlineServices: OnlineService[] = [
    {
      name: "SmallPDF",
      url: "https://smallpdf.com/pdf-to-jpg",
      description: "خدمة مجانية وآمنة لتحويل PDF إلى JPG",
      features: ["مجاني", "آمن", "سريع", "جودة عالية"]
    },
    {
      name: "ILovePDF",
      url: "https://www.ilovepdf.com/pdf_to_jpg",
      description: "أداة مجانية لتحويل PDF إلى صور JPG",
      features: ["مجاني", "بدون تسجيل", "معالجة سريعة"]
    },
    {
      name: "PDF24",
      url: "https://tools.pdf24.org/en/pdf-to-jpg",
      description: "محول PDF إلى JPG مجاني ومفتوح المصدر",
      features: ["مفتوح المصدر", "خصوصية عالية", "بدون حدود"]
    }
  ];

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== 'application/pdf') {
      toast({
        title: 'خطأ في نوع الملف',
        description: 'يرجى اختيار ملف PDF صالح.',
        variant: 'destructive',
      });
      return;
    }

    if (file.size > 50 * 1024 * 1024) { // 50MB limit
      toast({
        title: 'حجم الملف كبير',
        description: 'يرجى اختيار ملف أصغر من 50 ميجابايت.',
        variant: 'destructive',
      });
      return;
    }

    setSelectedFile(file);
    setShowOnlineServices(true);
  };

  const openOnlineService = (service: OnlineService) => {
    window.open(service.url, '_blank');
    toast({
      title: 'تم فتح الخدمة',
      description: `تم فتح ${service.name} في نافذة جديدة`,
    });
  };

  const clearAll = () => {
    setSelectedFile(null);
    setShowOnlineServices(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          تحويل PDF إلى JPG
        </CardTitle>
        <CardDescription>
          حول ملفات PDF إلى صور JPG عالية الجودة باستخدام أفضل الأدوات المجانية المتاحة.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* File Upload Section */}
        <div className="space-y-4">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <div className="space-y-2">
              <p className="text-sm text-gray-600">
                اختر ملف PDF لتحويله إلى صور JPG
              </p>
              <Button onClick={() => fileInputRef.current?.click()}>
                <Upload className="ml-2 h-4 w-4" />
                اختر ملف PDF
              </Button>
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept=".pdf,application/pdf"
                onChange={handleFileChange}
              />
            </div>
          </div>

          {selectedFile && (
            <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-3">
                <FileText className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-900">{selectedFile.name}</p>
                  <p className="text-sm text-blue-600">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} ميجابايت
                  </p>
                </div>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={clearAll}>
                  <Trash2 className="ml-2 h-4 w-4" />
                  مسح
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Online Services Section */}
        {showOnlineServices && (
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="h-5 w-5 text-blue-600" />
                <h3 className="font-semibold text-blue-900">خدمات التحويل المجانية</h3>
              </div>
              <p className="text-sm text-blue-700 mb-4">
                نوصي باستخدام إحدى الخدمات المجانية التالية لتحويل ملف PDF الخاص بك إلى صور JPG:
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {onlineServices.map((service, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-3 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between">
                    <h4 className="font-semibold text-gray-900">{service.name}</h4>
                    <ExternalLink className="h-4 w-4 text-gray-400" />
                  </div>
                  <p className="text-sm text-gray-600">{service.description}</p>
                  <div className="flex flex-wrap gap-1">
                    {service.features.map((feature, featureIndex) => (
                      <span
                        key={featureIndex}
                        className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                  <Button
                    className="w-full"
                    onClick={() => openOnlineService(service)}
                  >
                    <ExternalLink className="ml-2 h-4 w-4" />
                    استخدم {service.name}
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium mb-2">تعليمات الاستخدام:</h4>
          <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
            <li>اختر ملف PDF من جهازك (حد أقصى 50 ميجابايت)</li>
            <li>ستظهر لك قائمة بأفضل الخدمات المجانية لتحويل PDF إلى JPG</li>
            <li>اختر الخدمة التي تفضلها وانقر على "استخدم الخدمة"</li>
            <li>ستفتح الخدمة في نافذة جديدة حيث يمكنك رفع ملفك وتحويله</li>
            <li>جميع الخدمات المقترحة مجانية وآمنة وتحافظ على خصوصيتك</li>
          </ul>
        </div>

        {/* Security Notice */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <FileText className="h-5 w-5 text-green-600" />
            <h4 className="font-medium text-green-900">ملاحظة أمنية</h4>
          </div>
          <p className="text-sm text-green-700">
            جميع الخدمات المقترحة تحذف ملفاتك تلقائياً بعد فترة قصيرة من التحويل لضمان خصوصيتك وأمان بياناتك.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
