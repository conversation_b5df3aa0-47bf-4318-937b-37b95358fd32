'use client';

import { useState, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, Download, Trash2, AlertTriangle, FileText, Image as ImageIcon, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { pdfjs } from 'react-pdf';

// Set up the worker
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

interface ConvertedImage {
  pageNumber: number;
  dataUrl: string;
  fileName: string;
}

export function PdfToJpgTool() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [convertedImages, setConvertedImages] = useState<ConvertedImage[]>([]);
  const [isConverting, setIsConverting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);
    setConvertedImages([]);
    setTotalPages(0);
    setCurrentPage(0);

    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== 'application/pdf') {
      setError('يرجى اختيار ملف PDF صالح.');
      return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      setError('حجم الملف كبير جداً. يرجى اختيار ملف أصغر من 10 ميجابايت.');
      return;
    }

    setSelectedFile(file);
  };

  const convertPdfToImages = async () => {
    if (!selectedFile) return;

    setIsConverting(true);
    setError(null);
    setConvertedImages([]);
    setCurrentPage(0);

    try {
      console.log('Starting PDF conversion for file:', selectedFile.name);

      const arrayBuffer = await selectedFile.arrayBuffer();
      console.log('File loaded, size:', arrayBuffer.byteLength);

      const loadingTask = pdfjs.getDocument({
        data: arrayBuffer,
      });

      const pdf = await loadingTask.promise;
      const numPages = pdf.numPages;
      setTotalPages(numPages);
      console.log('PDF loaded successfully, pages:', numPages);

      const images: ConvertedImage[] = [];

      for (let pageNum = 1; pageNum <= numPages; pageNum++) {
        setCurrentPage(pageNum);
        console.log('Processing page:', pageNum);

        const page = await pdf.getPage(pageNum);
        const viewport = page.getViewport({ scale: 2.0 }); // Higher scale for better quality

        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');

        if (!context) {
          throw new Error('فشل في إنشاء canvas context');
        }

        canvas.height = viewport.height;
        canvas.width = viewport.width;

        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };

        await page.render(renderContext).promise;
        console.log('Page rendered:', pageNum);

        const dataUrl = canvas.toDataURL('image/jpeg', 0.9);
        const fileName = `${selectedFile.name.replace('.pdf', '')}_page_${pageNum}.jpg`;

        images.push({
          pageNumber: pageNum,
          dataUrl,
          fileName,
        });
      }

      setConvertedImages(images);
      console.log('Conversion completed successfully');
      toast({
        title: 'تم التحويل بنجاح!',
        description: `تم تحويل ${numPages} صفحة إلى صور JPG.`,
      });
    } catch (err) {
      console.error('PDF conversion error:', err);
      let errorMessage = 'فشل في تحويل ملف PDF. ';

      if (err instanceof Error) {
        if (err.message.includes('Invalid PDF')) {
          errorMessage += 'الملف ليس PDF صالح.';
        } else if (err.message.includes('password')) {
          errorMessage += 'الملف محمي بكلمة مرور.';
        } else if (err.message.includes('PDF.js library')) {
          errorMessage += 'خطأ في تحميل مكتبة PDF.js.';
        } else {
          errorMessage += `خطأ: ${err.message}`;
        }
      } else {
        errorMessage += 'يرجى التأكد من أن الملف صالح وغير محمي بكلمة مرور.';
      }

      setError(errorMessage);
    } finally {
      setIsConverting(false);
      setCurrentPage(0);
    }
  };

  const downloadImage = (image: ConvertedImage) => {
    const link = document.createElement('a');
    link.href = image.dataUrl;
    link.download = image.fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: 'تم التحميل',
      description: `تم تحميل ${image.fileName}`,
    });
  };

  const downloadAllImages = () => {
    convertedImages.forEach((image, index) => {
      setTimeout(() => {
        downloadImage(image);
      }, index * 100); // Small delay between downloads
    });
  };

  const clearAll = () => {
    setSelectedFile(null);
    setConvertedImages([]);
    setError(null);
    setTotalPages(0);
    setCurrentPage(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          تحويل PDF إلى JPG
        </CardTitle>
        <CardDescription>
          حول ملفات PDF إلى صور JPG عالية الجودة. يمكنك تحويل جميع الصفحات أو صفحات محددة.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* File Upload Section */}
        <div className="space-y-4">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <div className="space-y-2">
              <p className="text-sm text-gray-600">
                اختر ملف PDF لتحويله إلى صور JPG
              </p>
              <Button onClick={() => fileInputRef.current?.click()}>
                <Upload className="ml-2 h-4 w-4" />
                اختر ملف PDF
              </Button>
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept=".pdf,application/pdf"
                onChange={handleFileChange}
              />
            </div>
          </div>

          {selectedFile && (
            <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-3">
                <FileText className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-900">{selectedFile.name}</p>
                  <p className="text-sm text-blue-600">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} ميجابايت
                  </p>
                </div>
              </div>
              <div className="flex gap-2">
                <Button onClick={convertPdfToImages} disabled={isConverting}>
                  {isConverting ? (
                    <>
                      <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                      جاري التحويل...
                    </>
                  ) : (
                    <>
                      <ImageIcon className="ml-2 h-4 w-4" />
                      تحويل إلى JPG
                    </>
                  )}
                </Button>
                <Button variant="outline" onClick={clearAll}>
                  <Trash2 className="ml-2 h-4 w-4" />
                  مسح
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Progress Section */}
        {isConverting && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>جاري تحويل الصفحة {currentPage} من {totalPages}</span>
              <span>{Math.round((currentPage / totalPages) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(currentPage / totalPages) * 100}%` }}
              />
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Results Section */}
        {convertedImages.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">
                الصور المحولة ({convertedImages.length} صفحة)
              </h3>
              <Button onClick={downloadAllImages} variant="outline">
                <Download className="ml-2 h-4 w-4" />
                تحميل جميع الصور
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {convertedImages.map((image) => (
                <div key={image.pageNumber} className="border rounded-lg p-4 space-y-3">
                  <div className="aspect-[3/4] bg-gray-100 rounded overflow-hidden">
                    <img
                      src={image.dataUrl}
                      alt={`صفحة ${image.pageNumber}`}
                      className="w-full h-full object-contain"
                    />
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">صفحة {image.pageNumber}</p>
                    <Button 
                      size="sm" 
                      className="w-full"
                      onClick={() => downloadImage(image)}
                    >
                      <Download className="ml-2 h-3 w-3" />
                      تحميل
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium mb-2">تعليمات الاستخدام:</h4>
          <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
            <li>اختر ملف PDF من جهازك (حد أقصى 10 ميجابايت)</li>
            <li>انقر على "تحويل إلى JPG" لبدء عملية التحويل</li>
            <li>ستظهر جميع صفحات PDF كصور JPG منفصلة</li>
            <li>يمكنك تحميل كل صورة على حدة أو تحميل جميع الصور مرة واحدة</li>
            <li>جودة الصور عالية ومناسبة للطباعة والمشاركة</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
