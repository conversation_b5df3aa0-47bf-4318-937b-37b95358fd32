'use client';

import { useState, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, Download, Trash2, AlertTriangle, FileText, Image as ImageIcon, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ConvertedImage {
  pageNumber: number;
  dataUrl: string;
  fileName: string;
}

export function PdfToJpgTool() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [convertedImages, setConvertedImages] = useState<ConvertedImage[]>([]);
  const [isConverting, setIsConverting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== 'application/pdf') {
      toast({
        title: 'خطأ في نوع الملف',
        description: 'يرجى اختيار ملف PDF صالح.',
        variant: 'destructive',
      });
      return;
    }

    if (file.size > 50 * 1024 * 1024) { // 50MB limit
      toast({
        title: 'حجم الملف كبير',
        description: 'يرجى اختيار ملف أصغر من 50 ميجابايت.',
        variant: 'destructive',
      });
      return;
    }

    setSelectedFile(file);
    setError(null);

    // Show instructions
    toast({
      title: 'تم اختيار الملف',
      description: 'يرجى اتباع التعليمات أدناه لتحويل PDF إلى JPG.',
    });
  };

  const clearAll = () => {
    setSelectedFile(null);
    setConvertedImages([]);
    setError(null);
    setTotalPages(0);
    setCurrentPage(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          تحويل PDF إلى JPG
        </CardTitle>
        <CardDescription>
          دليل شامل لتحويل ملفات PDF إلى صور JPG عالية الجودة باستخدام أدوات مجانية وآمنة.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* File Upload Section */}
        <div className="space-y-4">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <div className="space-y-2">
              <p className="text-sm text-gray-600">
                اختر ملف PDF لمعرفة أفضل طرق تحويله إلى JPG
              </p>
              <Button onClick={() => fileInputRef.current?.click()}>
                <Upload className="ml-2 h-4 w-4" />
                اختر ملف PDF
              </Button>
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept=".pdf,application/pdf"
                onChange={handleFileChange}
              />
            </div>
          </div>

          {selectedFile && (
            <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-3">
                <FileText className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-900">{selectedFile.name}</p>
                  <p className="text-sm text-blue-600">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} ميجابايت
                  </p>
                </div>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={clearAll}>
                  <Trash2 className="ml-2 h-4 w-4" />
                  مسح
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Method 1: Browser Built-in */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-center gap-2 mb-4">
            <ImageIcon className="h-6 w-6 text-blue-600" />
            <h3 className="text-lg font-semibold text-blue-900">الطريقة الأولى: استخدام المتصفح مباشرة</h3>
          </div>
          <div className="space-y-3 text-sm text-blue-800">
            <p className="font-medium">خطوات التحويل:</p>
            <ol className="list-decimal list-inside space-y-2 mr-4">
              <li>افتح ملف PDF في متصفحك (اسحب الملف إلى نافذة المتصفح)</li>
              <li>اضغط Ctrl+P (أو Cmd+P على Mac) لفتح نافذة الطباعة</li>
              <li>اختر "حفظ كـ PDF" أو "Microsoft Print to PDF"</li>
              <li>في خيارات الطباعة، اختر "صفحات مخصصة" لتحديد الصفحات</li>
              <li>احفظ كل صفحة منفصلة ثم استخدم أداة تحويل PDF إلى JPG</li>
            </ol>
            <div className="bg-blue-100 p-3 rounded mt-4">
              <p className="font-medium text-blue-900">💡 نصيحة:</p>
              <p>هذه الطريقة مجانية تماماً ولا تتطلب أي برامج إضافية!</p>
            </div>
          </div>
        </div>

        {/* Method 2: Online Tools */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <div className="flex items-center gap-2 mb-4">
            <Download className="h-6 w-6 text-green-600" />
            <h3 className="text-lg font-semibold text-green-900">الطريقة الثانية: أدوات التحويل المجانية</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white p-4 rounded-lg border">
              <h4 className="font-semibold text-green-800 mb-2">SmallPDF</h4>
              <p className="text-sm text-green-700 mb-3">أداة مجانية وآمنة لتحويل PDF إلى JPG</p>
              <Button
                size="sm"
                className="w-full"
                onClick={() => window.open('https://smallpdf.com/pdf-to-jpg', '_blank')}
              >
                استخدم SmallPDF
              </Button>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <h4 className="font-semibold text-green-800 mb-2">ILovePDF</h4>
              <p className="text-sm text-green-700 mb-3">تحويل سريع وبدون تسجيل</p>
              <Button
                size="sm"
                className="w-full"
                onClick={() => window.open('https://www.ilovepdf.com/pdf_to_jpg', '_blank')}
              >
                استخدم ILovePDF
              </Button>
            </div>
          </div>
        </div>

        {/* Method 3: Software */}
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
          <div className="flex items-center gap-2 mb-4">
            <FileText className="h-6 w-6 text-purple-600" />
            <h3 className="text-lg font-semibold text-purple-900">الطريقة الثالثة: برامج سطح المكتب</h3>
          </div>
          <div className="space-y-3 text-sm text-purple-800">
            <p className="font-medium">برامج مجانية موصى بها:</p>
            <ul className="list-disc list-inside space-y-2 mr-4">
              <li><strong>GIMP:</strong> برنامج تحرير صور مجاني يدعم فتح PDF وتصديره كصور</li>
              <li><strong>Adobe Acrobat Reader:</strong> يمكن تصدير الصفحات كصور من قائمة File</li>
              <li><strong>PDFtk:</strong> أداة سطر أوامر قوية لمعالجة ملفات PDF</li>
              <li><strong>Inkscape:</strong> يمكن فتح PDF وتصدير كل صفحة كصورة</li>
            </ul>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h4 className="font-medium mb-3 text-gray-900">نصائح مهمة للحصول على أفضل جودة:</h4>
          <ul className="text-sm text-gray-700 space-y-2 list-disc list-inside">
            <li>اختر دقة عالية (300 DPI أو أكثر) للطباعة</li>
            <li>استخدم دقة 150 DPI للعرض على الشاشة</li>
            <li>احفظ بصيغة JPG بجودة 90% للحصول على توازن بين الجودة وحجم الملف</li>
            <li>تأكد من أن ملف PDF غير محمي بكلمة مرور</li>
            <li>للملفات الكبيرة، قم بتحويل صفحات قليلة في كل مرة</li>
          </ul>
        </div>

        {/* Security Notice */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <AlertTriangle className="h-5 w-5 text-yellow-600" />
            <h4 className="font-medium text-yellow-900">ملاحظة أمنية</h4>
          </div>
          <p className="text-sm text-yellow-800">
            عند استخدام الأدوات المجانية عبر الإنترنت، تأكد من أن الموقع يحذف ملفاتك تلقائياً بعد التحويل.
            تجنب رفع المستندات الحساسة أو السرية للمواقع الخارجية.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
